const { createLogger, logError, logPerformance } = require('../shared/utils/logger');
const { ChatTypeEnum, CONFIG } = require('../shared/utils/constants');
const { getChatContext } = require('./chatHistoryService');

// Import static chatbot handlers
const {
  getCommunityEventsPrompt,
  getFamilyPrompt,
  getKnowledgePrompt,
  getUnifyPrompt,
} = require('../handlers/static');

// Create a logger for this module
const logger = createLogger('dynamicChatbot');

// Map chat types to their corresponding static handlers
const staticHandlerMap = {
  [ChatTypeEnum.COMMUNITY]: getCommunityEventsPrompt,
  [ChatTypeEnum.FAMILY]: getFamilyPrompt,
  [ChatTypeEnum.KNOWLEDGE]: getKnowledgePrompt,
  [ChatTypeEnum.UNIFY]: getUnifyPrompt,
};

/**
 * Lazy loader for the chatbot module to handle circular dependencies
 */
class ChatbotModuleLoader {
  static #instance = null;

  /**
   * Get the chatbot module instance
   * @returns {Object} The chatbot module
   */
  static getInstance() {
    if (!this.#instance) {
      this.#instance = require('./chatbot');
    }
    return this.#instance;
  }
}

/**
 * Validates input parameters for the dynamic prompt generation
 * @param {string} chatType - Type of chat
 * @param {string} question - User's question
 * @param {Object} promptData - Data for the prompt
 * @param {string} userId - User ID
 * @param {string} staticChatbotName - Name of the static chatbot function
 * @throws {Error} If validation fails
 */
function validateInput(chatType, question, promptData, userId, staticChatbotName) {
  const errors = [];

  if (!chatType || typeof chatType !== 'string') {
    errors.push('chatType must be a non-empty string');
  }

  if (!question || typeof question !== 'string' || question.length > CONFIG.MAX_QUESTION_LENGTH) {
    errors.push(
      `question must be a non-empty string with max length ${CONFIG.MAX_QUESTION_LENGTH}`
    );
  }

  if (!userId || typeof userId !== 'string') {
    errors.push('userId must be a non-empty string');
  }

  if (!staticChatbotName || typeof staticChatbotName !== 'string') {
    errors.push('staticChatbotName must be a non-empty string');
  }

  if (errors.length > 0) {
    throw new Error(`Invalid input: ${errors.join('; ')}`);
  }
}

/**
 * Creates a child logger with chat context
 * @param {string} chatType - Type of chat
 * @param {string} requestId - Unique request ID
 * @returns {Object} Configured logger instance
 */
function createRequestLogger(chatType, requestId) {
  return logger.child({
    component: 'chatHandler',
    chatType,
    requestId,
  });
}

/**
 * Handles the fallback to static chatbot when dynamic generation fails
 * @param {Object} params - Function parameters
 * @param {string} params.chatType - Type of chat
 * @param {string} params.staticChatbotName - Name of the static chatbot function
 * @param {string} params.question - User's question
 * @param {Object} params.promptData - Data for the prompt
 * @param {string} params.userId - User ID
 * @param {Object} params.apolloClient - Apollo client instance
 * @param {Object} params.logger - Logger instance
 * @returns {Promise<string>} Generated response from static chatbot
 */
async function handleStaticFallback({
  chatType,
  staticChatbotName,
  question,
  promptData,
  userId,
  apolloClient,
  logger,
}) {
  try {
    logger.info(`Attempting fallback to static chatbot for chat type: ${chatType}`);

    // Get the appropriate static handler based on chat type
    const staticHandler = staticHandlerMap[chatType];

    if (typeof staticHandler !== 'function') {
      throw new Error(`No static handler found for chat type: ${chatType}`);
    }

    // Call the static handler with the correct parameters including chatType
    const result = await staticHandler(question, promptData, userId, apolloClient, chatType);
    logger.info(
      `Fallback to static handler for ${chatType} ${result ? 'succeeded' : 'returned empty result'}`
    );

    return result || CONFIG.DEFAULT_ERROR_MESSAGE;
  } catch (error) {
    logger.error(`Static chatbot fallback failed`, error);
    return CONFIG.DEFAULT_ERROR_MESSAGE;
  }
}

/**
 * Generates a dynamic prompt with fallback to static version if needed
 * @param {Object} params - Function parameters
 * @param {string} params.chatType - Type of chat (Community, Family, Knowledge, Unify)
 * @param {string} params.question - User's question
 * @param {Object} params.promptData - Data to be used in the prompt
 * @param {string} params.userId - User ID
 * @param {string} params.staticChatbotName - Name of the static chatbot function
 * @param {Object} params.apolloClient - Apollo client instance
 * @returns {Promise<string>} Generated dynamic prompt or static fallback
 */
async function generateDynamicPrompt({
  chatType,
  question,
  promptData,
  userId,
  staticChatbotName,
  apolloClient,
  logger,
}) {
  // Ensure logger is defined, use console as fallback
  const safeLogger = logger || console;
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  const startTime = Date.now();

  // Create a child logger if available, otherwise use the base logger
  const loggerWithContext = safeLogger.child
    ? safeLogger.child({
        operation: 'generateDynamicPrompt',
        chatType,
        requestId,
        userId: userId || 'unknown',
      })
    : Object.assign({}, safeLogger, {
        debug: (...args) => safeLogger.debug(`[${requestId}]`, ...args),
        info: (...args) => safeLogger.info(`[${requestId}]`, ...args),
        warn: (...args) => safeLogger.warn(`[${requestId}]`, ...args),
        error: (...args) => safeLogger.error(`[${requestId}]`, ...args),
      });

  try {
    loggerWithContext.info('Starting dynamic prompt generation', { chatType, userId });

    loggerWithContext.debug('Validating input');
    validateInput(chatType, question, promptData, userId, staticChatbotName);

    loggerWithContext.debug('Generating dynamic prompt', {
      questionLength: question?.length,
      promptDataKeys: promptData ? Object.keys(promptData) : [],
    });

    const { getChatbotPrompt } = ChatbotModuleLoader.getInstance();
    const promptConfig = await getChatbotPrompt(chatType);

    // Fallback to static if no prompt config found
    if (!promptConfig?.promptText) {
      loggerWithContext.warn(
        `No prompt configuration found for ${chatType}, falling back to static prompt`
      );
      return handleStaticFallback({
        chatType,
        staticChatbotName,
        question,
        promptData,
        userId,
        apolloClient,
        logger: loggerWithContext,
      });
    }

    // Get chat history for context using the chat history service
    loggerWithContext.debug('Fetching chat history for context', { chatType });
    const chatHistory = await getChatContext(userId, { limit: 3, chatType });
    loggerWithContext.debug('Retrieved chat history', {
      chatType,
      hasHistory: !!chatHistory,
      historyLength: chatHistory?.length || 0,
      preview: chatHistory
        ? chatHistory.substring(0, 100) + (chatHistory.length > 100 ? '...' : '')
        : '',
    });

    // Generate dynamic prompt with template variables
    const templateContext = {
      userId,
      question,
      currentDate: new Date().toISOString(),
      chatHistory: chatHistory || 'No previous conversation history available.',
      promptData: JSON.stringify(promptData, null, 2),
    };

    const dynamicPrompt = Object.entries(templateContext).reduce(
      (prompt, [key, value]) => prompt.replace(new RegExp(`\\$\\{${key}\\}`, 'g'), String(value)),
      promptConfig.promptText
    );

    // Log the final prompt before sending to Bedrock
    loggerWithContext.debug('Final prompt being sent to Bedrock (dynamic)', {
      promptLength: dynamicPrompt.length,
      promptPreview: dynamicPrompt.substring(0, 200) + (dynamicPrompt.length > 200 ? '...' : ''),
      templateVariables: Object.keys(templateContext),
      hasChatHistory: !!chatHistory,
      chatHistoryLength: chatHistory?.length || 0,
    });

    // Log performance metrics
    const executionTime = Date.now() - startTime;
    loggerWithContext.info(`Generated dynamic prompt in ${executionTime}ms`, {
      promptLength: dynamicPrompt.length,
      preview: dynamicPrompt.substring(0, 150) + (dynamicPrompt.length > 150 ? '...' : ''),
    });

    logPerformance(loggerWithContext, 'generateDynamicPrompt', startTime, {
      chatType,
      success: true,
    });

    return dynamicPrompt;
  } catch (error) {
    const executionTime = Date.now() - startTime;
    loggerWithContext.error(`Failed to generate dynamic prompt after ${executionTime}ms`, error);

    logError(loggerWithContext, error, 'generateDynamicPrompt', {
      chatType,
      durationMs: executionTime,
    });

    // Attempt fallback to static chatbot
    return handleStaticFallback({
      chatType,
      staticChatbotName,
      question,
      promptData,
      userId,
      apolloClient,
      logger: loggerWithContext,
    });
  }
}

/**
 * Creates a chatbot handler for a specific chat type
 * @param {string} chatType - Type of chat
 * @param {string} staticChatbotName - Name of the static chatbot function
 * @returns {Function} Configured chatbot handler function
 */
function createChatbotHandler(chatType, staticChatbotName) {
  return async function (question, promptData, userId, apolloClient) {
    return generateDynamicPrompt({
      chatType,
      question,
      promptData,
      userId,
      staticChatbotName,
      apolloClient,
    });
  };
}

// Export configured chatbot handlers
module.exports = {
  communityEventsChatbotDynamic: createChatbotHandler(
    ChatTypeEnum.COMMUNITY,
    'getCommunityEventsPrompt'
  ),
  familyEventsChatbotDynamic: createChatbotHandler(ChatTypeEnum.FAMILY, 'getFamilyPrompt'),
  knowledgeChatbotDynamic: createChatbotHandler(ChatTypeEnum.KNOWLEDGE, 'getKnowledgePrompt'),
  unifyChatbotDynamic: createChatbotHandler(ChatTypeEnum.UNIFY, 'getUnifyPrompt'),
};
