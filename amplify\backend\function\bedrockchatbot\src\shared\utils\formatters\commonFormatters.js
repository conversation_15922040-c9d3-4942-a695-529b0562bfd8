const { createLogger } = require('../logger');
const logger = createLogger('commonFormatters');

/**
 * Formats a date string to a human-readable format
 * @param {string} dateString - ISO date string
 * @param {Object} options - Formatting options
 * @param {boolean} options.includeTime - Whether to include time (default: true)
 * @returns {string} Formatted date string
 */
function formatDate(dateString, options = {}) {
  if (!dateString) return 'Date not specified';

  const { includeTime = true } = options;
  const date = new Date(dateString);

  const formatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };

  if (includeTime) {
    formatOptions.hour = '2-digit';
    formatOptions.minute = '2-digit';
  }

  return date.toLocaleDateString('en-US', formatOptions);
}

/**
 * Formats user information section
 * @param {Object} user - User data
 * @param {string} title - Section title (default: "User Information")
 * @returns {string} Formatted user info
 */
function formatUserInfo(user, title = 'User Information') {
  if (!user) return '';

  let userInfo = `## ${title}\n`;

  if (user.givenName || user.familyName) {
    userInfo += `- **Name**: ${user.givenName || ''} ${user.familyName || ''}\n`;
  }

  if (user.id) {
    userInfo += `- **User ID**: ${user.id}\n`;
  }

  if (user.email) {
    userInfo += `- **Email**: ${user.email}\n`;
  }

  if (user.phone) {
    userInfo += `- **Phone**: ${user.phone}\n`;
  }

  if (user.memberSince) {
    userInfo += `- **Member Since**: ${formatDate(user.memberSince)}\n`;
  }

  if (user.accessLevel) {
    userInfo += `- **Access Level**: ${user.accessLevel}\n`;
  }

  return userInfo + '\n';
}

/**
 * Formats metadata section
 * @param {Object} metadata - Metadata object
 * @param {string} timestamp - Optional custom timestamp
 * @returns {string} Formatted metadata
 */
function formatMetadata(metadata, timestamp = null) {
  if (!metadata) return '';

  let metadataStr = '---\n';

  if (timestamp) {
    metadataStr += `*Last updated: ${formatDate(timestamp)}*\n`;
  } else if (metadata.timestamp) {
    metadataStr += `*Last updated: ${formatDate(metadata.timestamp)}*\n`;
  } else {
    metadataStr += `*Last updated: ${new Date().toLocaleString()}*\n`;
  }

  if (metadata.chatType) {
    metadataStr += `*Chat Type: ${metadata.chatType}*\n`;
  }

  return metadataStr;
}

/**
 * Formats organization data into a readable string
 * @param {Object} org - Organization data
 * @param {Object} options - Formatting options
 * @param {string} options.indent - Indentation string (default: "  ")
 * @returns {string} Formatted organization info
 */
function formatOrganization(org, options = {}) {
  if (!org) return 'No organization data';

  const { indent = '  ' } = options;

  let orgStr = `\n${indent}- **${org.name || 'Unnamed Organization'}**\n`;

  if (org.description) {
    orgStr += `${indent}  ${org.description}\n`;
  }

  if (org.website) {
    orgStr += `${indent}  Website: ${org.website}\n`;
  }

  if (org.industry) {
    orgStr += `${indent}  Industry: ${org.industry}\n`;
  }

  if (org.size) {
    orgStr += `${indent}  Size: ${org.size} employees\n`;
  }

  if (org.cityId) {
    orgStr += `${indent}  Location: ${org.cityId}\n`;
  }

  if (org.membership) {
    orgStr += `${indent}  Your Membership:\n`;
    if (org.membership.currentImpactScore !== undefined) {
      orgStr += `${indent}    - Impact Score: ${org.membership.currentImpactScore}\n`;
    }
    if (org.membership.MVPTokens !== undefined) {
      orgStr += `${indent}    - MVP Tokens: ${org.membership.MVPTokens}\n`;
    }
    if (org.membership.fundTokens !== undefined) {
      orgStr += `${indent}    - Fund Tokens: ${org.membership.fundTokens}\n`;
    }
  }

  return orgStr;
}

/**
 * Formats event data into a readable string
 * @param {Object} event - Event data
 * @param {Object} options - Formatting options
 * @param {string} options.indent - Indentation string (default: "  ")
 * @returns {string} Formatted event info
 */
function formatEvent(event, options = {}) {
  if (!event) return 'No event data';

  const { indent = '  ' } = options;

  let eventStr = `\n${indent}- **${event.name || 'Unnamed Event'}**\n`;

  if (event.startDateTime) {
    eventStr += `${indent}  When: ${formatDate(event.startDateTime)}`;
    if (event.endDateTime) {
      eventStr += ` to ${formatDate(event.endDateTime)}\n`;
    } else {
      eventStr += '\n';
    }
  }

  if (event.description) {
    eventStr += `${indent}  ${event.description}\n`;
  }

  if (event.location) {
    eventStr += `${indent}  Location: ${event.location}\n`;
  }

  if (event.status) {
    eventStr += `${indent}  Status: ${event.status}\n`;
  }

  return eventStr;
}

/**
 * Formats a list of items with a section header
 * @param {Array} items - Array of items to format
 * @param {string} title - Section title
 * @param {Function} formatter - Function to format each item
 * @returns {string} Formatted section
 */
function formatSection(items, title, formatter) {
  if (!items || !Array.isArray(items) || items.length === 0) {
    return '';
  }

  let section = `## ${title} (${items.length})\n`;

  if (typeof formatter === 'function') {
    items.forEach((item) => {
      section += formatter(item);
    });
  } else {
    items.forEach((item) => {
      section += `- ${item}\n`;
    });
  }

  return section + '\n';
}

/**
 * Wraps formatter execution with error handling
 * @param {Function} formatterFn - The formatter function to execute
 * @param {*} data - Data to format
 * @param {string} fallbackMessage - Message to return on error
 * @param {string} loggerName - Logger name for error reporting
 * @returns {string} Formatted data or error message
 */
function withErrorHandling(formatterFn, data, fallbackMessage, loggerName) {
  try {
    return formatterFn(data);
  } catch (error) {
    logger.error(`Error in ${loggerName}:`, {
      error: error.message,
      stack: error.stack,
      dataType: typeof data,
      dataKeys: data ? Object.keys(data) : [],
    });
    return fallbackMessage;
  }
}

module.exports = {
  formatDate,
  formatUserInfo,
  formatMetadata,
  formatOrganization,
  formatEvent,
  formatSection,
  withErrorHandling,
};
