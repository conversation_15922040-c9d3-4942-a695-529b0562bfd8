const {
  formatDate,
  formatUserInfo,
  formatMetadata,
  formatOrganization,
  withErrorHandling,
} = require('./commonFormatters');

/**
 * Converts community query data into a meaningful summary
 * @param {Object} data - Raw community data from the query
 * @returns {string} Formatted summary of the community data
 */
function formatCommunityData(data) {
  if (!data) return 'No community data available';

  return withErrorHandling(
    (data) => {
      let summary = '';

      // User Information
      summary += formatUserInfo({
        givenName: data.givenName,
        familyName: data.familyName,
        id: data.id,
      });

      // Process user associations
      if (data.userAssociations?.items?.length > 0) {
        const organizations = data.userAssociations.items
          .filter((item) => item.type === 'Organization' && item.organization)
          .map((item) => item.organization);

        const people = data.userAssociations.items
          .filter((item) => item.type === 'Person')
          .map((item) => ({
            type: 'Person',
            createdAt: item.createdAt,
          }));

        const businesses = data.userAssociations.items
          .filter((item) => item.type === 'Business')
          .map((item) => ({
            type: 'Business',
            createdAt: item.createdAt,
          }));

        // Format Organizations
        if (organizations.length > 0) {
          summary += `## Organizations (${organizations.length})\n`;
          organizations.forEach((org) => {
            summary += formatOrganization(org);
          });
          summary += '\n';
        }

        // Format Personal Connections
        if (people.length > 0) {
          summary += `## Personal Connections (${people.length})\n`;
          people.forEach((person) => {
            summary += `- Connection established on ${formatDate(person.createdAt)}\n`;
          });
          summary += '\n';
        }

        // Format Business Connections
        if (businesses.length > 0) {
          summary += `## Business Connections (${businesses.length})\n`;
          businesses.forEach((business) => {
            summary += `- Business connection established on ${formatDate(business.createdAt)}\n`;
          });
          summary += '\n';
        }
      } else {
        summary += 'No community connections found.\n\n';
      }

      // Add metadata
      summary += formatMetadata(data._metadata);

      return summary;
    },
    data,
    'Error processing community data. Please try again later.',
    'communityDataFormatter'
  );
}

module.exports = {
  formatCommunityData,
};
